# Check the number of arguments
if [ $# -ne 3 ]; then
    echo "Usage: $0 {dev|sit|prd} {fg|bg} {java_path}"
    echo "** fg means foreground process. bg means background process."
    exit 1
fi

declare env=$1
declare exec_mode=$2
declare java_path=$3

# Validate the argument value
if [ "${env}" != "dev" ] && [ "${env}" != "sit" ] && [ "${env}" != "prd" ]; then
    echo "Invalid environment. Allowed values: dev, sit, prd"
    exit 1
fi

if [ "${exec_mode}" != "fg" ] && [ "${exec_mode}" != "bg" ]; then
    echo "Invalid execution mode. Allowed values: fg, bg"
    exit 1
fi

if command -v ${java_path} &>/dev/null; then
    declare script_path=$(dirname "$(readlink -f "$0")")
    cd ${script_path}/${env}
    declare java_cmd="${java_path} -Dspring.profiles.active=${env} -jar ${script_path}/${env}/chatops-spring-$(cat chatops_version.txt).jar"
    if [ "${exec_mode}" == "fg" ]; then
        ${java_cmd}
    else
        nohup ${java_cmd} >${script_path}/${env}/chatops_nohup.log 2>&1 &
        ps -ef | grep "java -Dspring.profiles.active=${env}" | grep -v grep
    fi
else
    echo "Cannot find java. Please check"
fi
