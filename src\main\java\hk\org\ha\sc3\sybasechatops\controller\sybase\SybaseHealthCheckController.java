package hk.org.ha.sc3.sybasechatops.controller.sybase;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.controller.BaseHealthCheckController;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.service.CommandGroupService;
import hk.org.ha.sc3.sybasechatops.service.PDFService;

@RestController
@RequestMapping("/sybase/health_check")
public class SybaseHealthCheckController extends BaseHealthCheckController {

    /* A constructor to accept all private fields */
    public SybaseHealthCheckController(PDFService pdfService,
            DatabaseRepository databaseRepository, CommandGroupService commandGroupService) {
        super(pdfService, databaseRepository, commandGroupService);
    }

    @Override
    public DatabaseTypeEnum getType() {
        return DatabaseTypeEnum.ASE;
    }

}
