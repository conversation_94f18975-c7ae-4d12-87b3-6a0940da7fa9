#modify the masterDirector at oem.sh, json_handler.py and json_handler_str.py; modify the serverdev.hadev/server.ha and the OEM link to change environment
yamlFile="$2"
masterDirectory=$(awk '/oemagent:/,/shMasterDirectory:/' ${yamlFile} | grep 'shMasterDirectory:' | sed 's/.*shMasterDirectory: *//' | tr -d '\r')
username=$(awk '/oemagent:/,/username:/' ${yamlFile} | grep 'username:' | sed 's/.*username: *//' | tr -d '\r')
cred=$(awk '/oemagent:/,/password:/' ${yamlFile} | grep 'password:' | sed 's/.*password: *//' | tr -d '\r')
baseURL=$(awk '/oemagent:/,/baseUrl:/' ${yamlFile} | grep 'baseUrl:' | sed "s/.*baseUrl: *'//; s/'//g" | tr -d '\r')

#set to PRD suffix or non-PRD suffix
#suffix=".serverdev.hadev.org.hk:1833"
suffix=$(awk '/oemagent:/,/suffix:/' ${yamlFile} | grep 'suffix:' | sed "s/.*suffix: *'//; s/'//g" | tr -d '\r')

curl -s -k -X GET -u ${username}:${cred} --header "Accept: application/json" --header "Content-Type: application/json" ${baseURL}em/api/targets > ${masterDirectory}all_Target.json

input="$1"

# Extract the part before the first period
short_name="${input%%.*}"
target_prefix=""

# Check if the string contains a period
if [[ $input == *"."* ]]; then
  # If it does, use the cut command to extract the string after the first period
  target_prefix=".$(echo $input | cut -d'.' -f2-)"
else
  target_prefix=""
fi

declare -a array1

for (( i=0; i<${#target_prefix}; i++ )); do
  # Add each character to the array
  array1[i]=${target_prefix:$i:1}
done

declare -a array2

# Use a for loop to iterate through each character of the string
for (( i=0; i<${#suffix}; i++ )); do
  # Add each character to the array
  array2[i]=${suffix:$i:1}
done

declare -a array3

# Compare the arrays and append different elements to the new array
for i in "${array1[@]}"; do
  found=false
  for j in "${array2[@]}"; do
    if [ $i = $j ]; then
      found=true
      break
    fi
  done
  if [ "$found" = false ]; then
    array3+=($i)
  fi
done

# If one array is longer than the other, append its extra elements to the new array
if [ ${#array1[@]} -gt ${#array2[@]} ]; then
  for ((i=${#array2[@]}; i<${#array1[@]}; i++)); do
    array3+=(${array1[i]})
  done
elif [ ${#array2[@]} -gt ${#array1[@]} ]; then
  for ((i=${#array1[@]}; i<${#array2[@]}; i++)); do
    array3+=(${array2[i]})
  done
fi

#for element in "${array3[@]}"
#do
#  echo "${array3[@]}"
#  echo "$element"
#done

# Iterate over the array and append each element to the string
for element in "${array3[@]}"; do
  input="$input $element"
done

# Use the tr command to remove spaces
input=$(echo "$input" | tr -d ' ')

server_name="$input"

server_id=$(python ${masterDirectory}json_handler.py ${masterDirectory}all_Target.json id name ${server_name} ${masterDirectory})

server_id="${server_id#[}"
server_id="${server_id%]}"
#IFS=', ' read -ra array <<< "$server_id"

server_id="${server_id#u\'}"
server_id="${server_id%\'}"
server_id=$(echo $server_id | sed "s/^'//")

curl -s -k -X GET -u ${username}:${cred} --header "Accept: application/json" --header "Content-Type: application/json" ${baseURL}em/api/targets/${server_id} > ${masterDirectory}Target.json

agent_status=$(python ${masterDirectory}json_handler_str.py ${masterDirectory}Target.json ${masterDirectory})
echo $agent_status > ${masterDirectory}result.txt
