INSERT INTO `health_check`.`chatops_command_group` (`id`, `cmd_type`, `db_type`, `enabled`) VALUES
('CIMS_HEALTH_CHECK', 'HOST', 'OR<PERSON><PERSON>', 1);

INSERT INTO `health_check`.`chatops_command` (`id`, `command`, `enabled`, `user`, `timeout`, `cmd_type`, `return_type`) VALUES
('ORA_CIMS_HEALTH_CHECK', 'odc-ora-cims-healthcheck', 1, NULL, NULL, 'ANSIBLE', 'WEBHOOK');


INSERT INTO `health_check`.`chatops_command_group_mapping` (`id`,`command_id`, `command_group_id`, `pdf_title`, `wait_for_id`, `exec_mode`, `sequence`) VALUES
(35, 'ORA_CIMS_HEALTH_CHECK', 'CIMS_HEALTH_CHECK', 'CIMS health check result', NULL, NULL, 1);