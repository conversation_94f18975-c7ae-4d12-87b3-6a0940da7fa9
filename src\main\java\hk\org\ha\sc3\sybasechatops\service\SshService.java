package hk.org.ha.sc3.sybasechatops.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.SocketTimeoutException;
import java.net.URISyntaxException;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Collections;
import java.util.EnumSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.PreDestroy;

import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.channel.ClientChannel;
import org.apache.sshd.client.channel.ClientChannelEvent;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.common.util.io.output.NoCloseOutputStream;
import org.springframework.stereotype.Service;

import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SshService implements ICommand {

    private OsPasswordService osPasswordService;
    private SshClient client;

    /* Constructor */
    public SshService(OsPasswordService osPasswordService) throws URISyntaxException {
        this.osPasswordService = osPasswordService;
        client = SshClient.setUpDefaultClient();
        client.start();
    }

    public CommandResult exec(String user, String hostName, String command, Integer timeout)
            throws IOException {
        ByteArrayOutputStream stdout = new ByteArrayOutputStream();
        ByteArrayOutputStream stderr = new ByteArrayOutputStream();
        int exitCode = -99999;
        double hcElapsedTime = -99999;
        long hcEndTime;
        long hcStartTime = System.currentTimeMillis();
        log.debug("Start timestamp: {}", hcStartTime);

        try (ClientSession session = client.connect(user, hostName, 22)
                .verify(10, TimeUnit.SECONDS)
                .getSession()) {
            session.addPasswordIdentity(this.osPasswordService.getPassword(user, hostName));
            session.auth().verify();

            try (OutputStream channelErr = new NoCloseOutputStream(stderr);
                    OutputStream channelOut = new NoCloseOutputStream(stdout);
                    ClientChannel channel = session.createExecChannel(command)) {
                channel.setOut(channelOut);
                channel.setErr(channelErr);
                channel.open().await();
                Set<ClientChannelEvent> REMOTE_COMMAND_WAIT_EVENTS = Collections
                        .unmodifiableSet(EnumSet.of(ClientChannelEvent.CLOSED));
                Collection<ClientChannelEvent> waitMask = channel.waitFor(REMOTE_COMMAND_WAIT_EVENTS,
                        TimeUnit.SECONDS.toMillis(timeout == null ? 0 : timeout));
                if (waitMask.contains(ClientChannelEvent.TIMEOUT)) {
                    throw new SocketTimeoutException("Failed to retrieve command result in time: " + command);
                }
                exitCode = channel.getExitStatus();
                ClientChannel.validateCommandExitStatusCode(command, exitCode);
            }
            hcEndTime = System.currentTimeMillis();
            log.debug("End timestamp: {}", hcEndTime);
        } catch (RemoteException | SocketTimeoutException e) {
            log.error("Command exec exception", e);
            hcEndTime = System.currentTimeMillis();
            log.debug("End timestamp (with error): {}", hcEndTime);
        }
        hcElapsedTime = (double) ((hcEndTime - hcStartTime)) / 1000;
        log.debug("Return Code: {}", exitCode);
        log.debug("Result: {}", stdout);
        log.debug("Error: {}", stderr);

        return CommandResult.builder().stdout(stdout.toString()).stderr(stderr.toString()).rc(exitCode)
                .elapsedTime(hcElapsedTime).command(command).build();
    }

    public CommandResult exec(Command command) throws IOException {
        CommandResult simpleResult = this.exec(command.getUser(), command.getHost(), command.getCommand(),
                command.getTimeout());
        return simpleResult;
    }

    @PreDestroy
    public void onDestroy() throws Exception {
        this.client.stop();
        log.debug("SSH client is stopped.");
    }

    @Override
    public CommandResult execByCommand(Command command) throws Exception {
        return this.exec(command);
    }

}
