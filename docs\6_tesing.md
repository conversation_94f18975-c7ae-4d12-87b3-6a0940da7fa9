# Testing Best Practices

## ⚡ **Quick Start**

```bash
# Run tests with coverage
mvn clean test

# Generate coverage report
mvn jacoco:report

# View HTML report
start target/site/jacoco/index.html
```

## 📊 **Coverage Reports Generated**

After running the commands above, you'll find:

- **`target/site/jacoco/index.html`** - Interactive HTML report (recommended for developers)
- **`target/site/jacoco/jacoco.xml`** - XML format (for CI/CD integration)
- **`target/site/jacoco/jacoco.csv`** - CSV format (for data analysis)

## 🧪 **Spring Boot Testing Strategy**

### 1. **Integration Tests** (Current Approach)
```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
class ServiceIntegrationTest {
    @Autowired
    private Service service;
    
    @Test
    void shouldIntegrateWithDatabase() {
        // Test with real Spring context
    }
}
```

### 2. **Unit Tests** (Fast, Isolated)
```java
@ExtendWith(MockitoExtension.class)
class ServiceUnitTest {
    @Mock
    private Repository repository;
    
    @InjectMocks
    private Service service;
    
    @Test
    void shouldProcessData() {
        // Test business logic in isolation
    }
}
```

### 3. **Web Layer Tests**
```java
@WebMvcTest(Controller.class)
class ControllerTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private Service service;
    
    @Test
    void shouldReturnExpectedResponse() throws Exception {
        mockMvc.perform(get("/api/endpoint"))
               .andExpect(status().isOk());
    }
}
```

### 4. **Repository Tests**
```java
@DataJpaTest
class RepositoryTest {
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private Repository repository;
    
    @Test
    void shouldFindByCustomQuery() {
        // Test database queries
    }
}
```

## 📈 **Coverage Metrics Explained**

- **Line Coverage** - Percentage of code lines executed
- **Branch Coverage** - Percentage of decision branches taken
- **Method Coverage** - Percentage of methods called
- **Class Coverage** - Percentage of classes instantiated

## 🎯 **Coverage Goals**

- **Unit Tests**: 80-90% line coverage
- **Integration Tests**: Focus on critical paths
- **Overall Project**: 60-70% (configured in pom.xml)

## 🛠 **Common Commands**

```bash
# Run tests only
mvn test

# Run tests + generate coverage
mvn clean verify

# Generate report from existing test data
mvn jacoco:report

# Check coverage thresholds
mvn jacoco:check@check

# Skip tests but generate report
mvn jacoco:report -DskipTests

# Run specific test class
mvn test -Dtest=AnsibleTests
```

## 📋 **Best Practices**

1. **Keep Tests Fast** - Use `@MockBean` for external dependencies
2. **Use Test Slices** - `@WebMvcTest`, `@DataJpaTest`, etc.
3. **Maintain Test Data** - Use `@Sql` or `@TestConfiguration`
4. **Regular Coverage Reviews** - Aim for meaningful coverage, not just numbers

### Recommendations for Improvement

1. **Add Unit Tests** for business logic
2. **Mock External Dependencies** in unit tests
3. **Add Controller Tests** with `@WebMvcTest`
4. **Test Error Scenarios** and edge cases
