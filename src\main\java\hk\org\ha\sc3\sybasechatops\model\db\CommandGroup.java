package hk.org.ha.sc3.sybasechatops.model.db;

import java.util.List;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.hibernate.annotations.Type;

import hk.org.ha.sc3.sybasechatops.constant.CmdGrpTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_command_group", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class CommandGroup {
    @Id
    private String id;

    @OneToMany(mappedBy="commandGroup")
    List<CommandGroupMapping> cmdGrpMappings;

    @Enumerated(EnumType.STRING)
    private DatabaseTypeEnum dbType;

    @Enumerated(EnumType.STRING)
    private CmdGrpTypeEnum cmdType;

    @Transient
    private String instance;

    @Transient
    private String host;

    @Type(type = "org.hibernate.type.NumericBooleanType")
    private boolean enabled;

}