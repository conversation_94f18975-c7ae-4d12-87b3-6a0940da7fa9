package hk.org.ha.sc3.sybasechatops.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

import hk.org.ha.sc3.sybasechatops.constant.ClouderaRoleEnum;
import hk.org.ha.sc3.sybasechatops.model.db.ClouderaComponent;
import hk.org.ha.sc3.sybasechatops.model.db.id.DatabaseId;

public interface ClouderaComponentRepository extends Repository<ClouderaComponent, DatabaseId> {
    /* find all ClouderaComponent records */
    List<ClouderaComponent> findAll();

    Optional<ClouderaComponent> findByIdHostAndRole(String host, ClouderaRoleEnum role);

    Optional<ClouderaComponent> findByIdHostAndIdInstance(String host, String instance);

    List<ClouderaComponent> findDistinctByRoleAndClusterClusterName(ClouderaRoleEnum role, String clusterName);

    @Query("SELECT DISTINCT role FROM ClouderaComponent")
    List<String> findDistinctRole();
}
