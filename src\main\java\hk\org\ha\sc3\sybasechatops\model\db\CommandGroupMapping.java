package hk.org.ha.sc3.sybasechatops.model.db;

import java.util.List;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.NaturalId;

import hk.org.ha.sc3.sybasechatops.constant.CmdExecModeEnum;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_command_group_mapping", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class CommandGroupMapping {

    @Id
    private long id;

    @ManyToOne
    Command command;

    @NaturalId
    @ManyToOne
    CommandGroup commandGroup;

    @NaturalId
    private int sequence;

    private CmdExecModeEnum execMode;

    private String pdfTitle;

    @OneToMany(mappedBy = "cmdGrpMapping")
    List<StoreVariable> storeVariables;

}