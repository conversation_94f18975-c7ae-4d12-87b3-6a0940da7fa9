package hk.org.ha.sc3.sybasechatops.model.db;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.IButtonView;
import hk.org.ha.sc3.sybasechatops.model.db.id.DatabaseId;

import java.util.List;
import javax.persistence.EmbeddedId;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_database", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class Database implements IButtonView {
    @EmbeddedId
    private DatabaseId id;

    @Enumerated(EnumType.STRING)
    private DatabaseTypeEnum type;

    @OneToMany(mappedBy="id.database")
    private List<DatabaseFunction> dbFunctions;

    @Override
    public String getButtonView() {
        return String.format("%s|%s", this.id.getHost(), this.id.getInstance());
    }
}
