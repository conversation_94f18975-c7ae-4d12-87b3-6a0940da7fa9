package hk.org.ha.sc3.sybasechatops.controller;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.hateoas.server.mvc.WebMvcLinkBuilder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.config.NotificationConfig;
import hk.org.ha.sc3.sybasechatops.config.SecretConfig;
import hk.org.ha.sc3.sybasechatops.constant.CmdGrpTypeEnum;
import hk.org.ha.sc3.sybasechatops.helper.ArrayUtils;
import hk.org.ha.sc3.sybasechatops.interfaces.IButtonView;
import hk.org.ha.sc3.sybasechatops.interfaces.IDatabaseType;
import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.HealthCheck;
import hk.org.ha.sc3.sybasechatops.model.HealthCheckKeyword;
import hk.org.ha.sc3.sybasechatops.model.HealthCheckKeywordItem;
import hk.org.ha.sc3.sybasechatops.model.Host;
import hk.org.ha.sc3.sybasechatops.model.SimpleMenu;
import hk.org.ha.sc3.sybasechatops.model.Reply;
import hk.org.ha.sc3.sybasechatops.model.db.CommandGroup;
import hk.org.ha.sc3.sybasechatops.model.db.Database;
import hk.org.ha.sc3.sybasechatops.repository.CommandGroupRepository;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public abstract class BaseMenuController implements IDatabaseType {

        private DatabaseRepository databaseRepository;
        private CommandGroupRepository commandGroupRepository;
        private final Reply unknownReply;
        private final Reply noInstanceFound;
        private Class<? extends BaseHealthCheckController> hcClazz;
        private NotificationConfig notificationConfig;
        private SecretConfig secretConfig;

        public BaseMenuController(DatabaseRepository databaseRepository, CommandGroupRepository commandGroupRepository,
                        Class<? extends BaseHealthCheckController> hcClazz, NotificationConfig notificationConfig,
                        SecretConfig secretConfig) {
                this.databaseRepository = databaseRepository;
                this.commandGroupRepository = commandGroupRepository;
                this.hcClazz = hcClazz;
                unknownReply = Reply.builder().msg("Unknown option").statusCode(200).build();
                noInstanceFound = Reply.builder().msg("No instance found with the provided wildcast")
                                .statusCode(200).build();
                this.notificationConfig = notificationConfig;
                this.secretConfig = secretConfig;
        }

        public abstract ChatopsRespBase getBaseResponse(String msgChain);

        @PostMapping
        public ChatopsRespBase baseMenu(@RequestBody ChatopsReq requestBody) {
                log.debug("Menu entered");
                log.debug(String.format("%-10s: %s", "Env", requestBody.getChatopsEnv()));
                log.debug(String.format("%-10s: %s", "CorpID", requestBody.getCorpID()));
                log.debug(String.format("%-10s: %s", "MsgChain", requestBody.getMsgChain()));
                log.debug(String.format("%-10s: %s", "Keyword", requestBody.getKeyword()));

                return getBaseResponse(requestBody.getMsgChain());
        }

        @PostMapping("instances")
        public ChatopsRespBase instanceSelectMenu(@RequestBody ChatopsReq requestBody) {
                log.debug("Enter /instances");
                String msgChainsStr = requestBody.getMsgChain();
                String instanceWildcast = requestBody.getKeyword();

                String[] msgChains = msgChainsStr.split("\\|");
                int instanceListIndex = ArrayUtils.getIndex(msgChains, "Instance List");
                String[] subMsgChains = Arrays.copyOfRange(msgChains, instanceListIndex + 1, msgChains.length);
                int indexPos = (msgChains.length - 1) - instanceListIndex;

                switch (indexPos) {
                        case 2:
                                return this.getCommandMenu(Collections.singletonList(CmdGrpTypeEnum.INSTANCE));
                        case 3:
                                String keyword = String.format("%s:%s|%s", subMsgChains[0], subMsgChains[1],
                                                subMsgChains[2]);
                                return postHealthCheckAction(keyword);
                        default:
                                return instanceDisplayMenu(instanceWildcast);
                }
        }

        @PostMapping("hosts")
        public ChatopsRespBase hostSelectMenu(@RequestBody ChatopsReq requestBody) {
                log.debug("Enter /hosts");
                String msgChainsStr = requestBody.getMsgChain();
                String hostWildcast = requestBody.getKeyword();

                String[] msgChains = msgChainsStr.split("\\|");
                int hostListIndex = ArrayUtils.getIndex(msgChains, "Host List");
                String[] subMsgChains = Arrays.copyOfRange(msgChains, hostListIndex + 1, msgChains.length);
                int indexPos = (msgChains.length - 1) - hostListIndex;

                switch (indexPos) {
                        case 1:
                                return this.getCommandMenu(
                                                new ArrayList<>(Arrays.asList(CmdGrpTypeEnum.INSTANCE,
                                                                CmdGrpTypeEnum.HOST)));
                        case 2:
                                /* find command using commandGroupRepository, check if cmdType is host */
                                CmdGrpTypeEnum cmdType = commandGroupRepository.findById(subMsgChains[1]).get()
                                                .getCmdType();
                                switch (cmdType) {
                                        case HOST:
                                                String keyword = String.format("%s:%s|%s", subMsgChains[0], "null",
                                                                subMsgChains[1]);
                                                return postHealthCheckAction(keyword);
                                        case INSTANCE:
                                                List<Database> databases = databaseRepository.findByTypeAndIdHost(
                                                                getType(),
                                                                subMsgChains[0]);
                                                /* use stream to get commands' id to array */

                                                String[] buttonList = ArrayUtils.append(databases.stream()
                                                                .map(d -> d.getId().getInstance())
                                                                .toArray(String[]::new),
                                                                "all");
                                                SimpleMenu instanceMenu = SimpleMenu.builder()
                                                                .msg("Please select an instance below")
                                                                .options(buttonList)
                                                                .statusCode(200)
                                                                .build();
                                                return instanceMenu;
                                        default:
                                                break;
                                }
                        case 3:
                                // host, instance, command
                                String keyword;
                                if (subMsgChains[2].equals("all")) {
                                        List<Database> databases = databaseRepository.findByTypeAndIdHost(getType(),
                                                        subMsgChains[0]);

                                        String instanceCommaStr = databases.stream().map(d -> d.getId().getInstance())
                                                        .collect(Collectors.joining(","));

                                        keyword = String.format("%s:%s|%s", subMsgChains[0], instanceCommaStr,
                                                        subMsgChains[1]);
                                } else {
                                        keyword = String.format("%s:%s|%s", subMsgChains[0], subMsgChains[2],
                                                        subMsgChains[1]);
                                }
                                return postHealthCheckAction(keyword);
                        default:
                                return hostDisplayMenu(hostWildcast);
                }

        }

        @PostMapping("projects")
        public ChatopsRespBase projectSelectMenu(@RequestBody ChatopsReq requestBody) {
                log.debug("Enter /projects");
                String msgChainsStr = requestBody.getMsgChain();
                String projectWildcast = requestBody.getKeyword();

                String[] msgChains = msgChainsStr.split("\\|");
                int projectListIndex = ArrayUtils.getIndex(msgChains, "Project List");
                String[] subMsgChains = Arrays.copyOfRange(msgChains, projectListIndex + 1, msgChains.length);
                int indexPos = (msgChains.length - 1) - projectListIndex;

                switch (indexPos) {
                        case 1:
                                if (subMsgChains[0].equals("all projects")) {
                                        return this.getCommandMenu(Collections.singletonList(CmdGrpTypeEnum.PROJECT));
                                } else {
                                        return this.getCommandMenu(new ArrayList<>(
                                                        Arrays.asList(CmdGrpTypeEnum.INSTANCE, CmdGrpTypeEnum.HOST)));
                                }

                        case 2:
                                CmdGrpTypeEnum cmdType = commandGroupRepository.findById(subMsgChains[1]).get()
                                                .getCmdType();
                                switch (cmdType) {
                                        case HOST:
                                                List<String> hosts = databaseRepository
                                                                .findHostsByTypeAndProj(getType(), subMsgChains[0]);
                                                String[] hostButtonList = ArrayUtils.append(
                                                                hosts.stream().toArray(String[]::new), "all hosts");
                                                return SimpleMenu.builder()
                                                                .msg("Please select host(s) below")
                                                                .options(hostButtonList)
                                                                .statusCode(200)
                                                                .build();
                                        case INSTANCE:
                                                List<Database> databases = databaseRepository
                                                                .findByTypeAndProj(getType(), subMsgChains[0]);
                                                String[] instanceButtonList = ArrayUtils.append(databases.stream()
                                                                .map(item -> item.getButtonView())
                                                                .toArray(String[]::new), "all instances");
                                                return SimpleMenu.builder()
                                                                .msg("Please select an instance below")
                                                                .options(instanceButtonList)
                                                                .statusCode(200)
                                                                .build();
                                        case PROJECT:
                                                // msg chain sample: "@itbot|Oracle|Project Search|Project List|all
                                                // projects|GRAFANA_STATUS_BY_PROJ
                                                return postHealthCheckAction(
                                                                String.format("%s:%s|%s", "null", "null",
                                                                                subMsgChains[1]));
                                        default:
                                                return unknownReply;
                                }

                        case 3:
                                switch (subMsgChains[2]) {
                                        // msg chain sample: "@itbot|Oracle|Project Search|Project List|DBS|ORAROLE|all
                                        // instances"
                                        case "all instances":
                                                List<Database> databases = databaseRepository.findByTypeAndProj(
                                                                getType(),
                                                                subMsgChains[0]);

                                                String instanceCommaStr = databases.stream()
                                                                .map(d -> d.getId().getHost() + ":"
                                                                                + d.getId().getInstance())
                                                                .collect(Collectors.joining("/"));

                                                return postHealthCheckAction(String.format("%s|%s", instanceCommaStr,
                                                                subMsgChains[1]));
                                        case "all hosts":
                                                List<String> hosts = databaseRepository.findHostsByTypeAndProj(
                                                                getType(),
                                                                subMsgChains[0]);

                                                String hostListStr = hosts.stream()
                                                                .map(d -> d + ":null")
                                                                .collect(Collectors.joining("/"));
                                                return postHealthCheckAction(
                                                                String.format("%s|%s", hostListStr, subMsgChains[1]));
                                        default:
                                                String keyword = String.format("%s:%s|%s", subMsgChains[2],
                                                                "null",
                                                                subMsgChains[1]);

                                                return postHealthCheckAction(keyword);
                                }
                        case 4:
                                String keyword = String.format("%s:%s|%s", subMsgChains[2],
                                                subMsgChains[3],
                                                subMsgChains[1]);
                                return postHealthCheckAction(keyword);
                        default:
                                List<String> projectCodeList = this.databaseRepository
                                                .findDistinctProjectByTypeAndProjLike(getType(),
                                                                projectWildcast);
                                String[] projectOptions;
                                if ("all".equalsIgnoreCase(projectWildcast)) {
                                        projectOptions = ArrayUtils.append(
                                                        projectCodeList.stream().toArray(String[]::new),
                                                        "all projects");
                                } else {
                                        projectOptions = projectCodeList.stream().toArray(String[]::new);
                                }

                                return SimpleMenu.builder().msg("Please select project below").options(projectOptions)
                                                .statusCode(200).build();
                }
        }

        public ChatopsRespBase postHealthCheckAction(String keyword) {
                URI uri;

                uri = WebMvcLinkBuilder.linkTo(
                                WebMvcLinkBuilder.methodOn(hcClazz)
                                                .dbHealthCheck(null))
                                .toUri();

                HealthCheck dbHealthCheck = HealthCheck.builder().keyword(keyword)
                                .msg("Health check processing (KEYWORD)")
                                .url(uri.toString())
                                .statusCode(200)
                                .token(secretConfig.getToken())
                                .build();
                return dbHealthCheck;
        }

        public ChatopsRespBase postApprovedHealthCheckAction(String keyword, String shortDesc) {
                URI uri;

                uri = WebMvcLinkBuilder.linkTo(
                                WebMvcLinkBuilder.methodOn(hcClazz)
                                                .dbHealthCheck(null))
                                .toUri();

                HealthCheck dbHealthCheck = HealthCheck.builder().keyword(keyword)
                                .msg("Health check processing (KEYWORD)")
                                .url(uri.toString())
                                .statusCode(200)
                                .token(secretConfig.getToken())
                                .notify(this.notificationConfig.getEmail())
                                .shortDesc(shortDesc)
                                .build();
                return dbHealthCheck;
        }

        private ChatopsRespBase hostDisplayMenu(String hostWildCast) {
                List<String> databases = (this.databaseRepository.findDistinctHostByTypeAndIdHostLike(
                                getType(),
                                hostWildCast));

                List<IButtonView> hostButtons = databases.stream().map(element -> (IButtonView) new Host(element))
                                .collect(Collectors.toList());

                URI uri;
                try {
                        uri = WebMvcLinkBuilder.linkTo(
                                        WebMvcLinkBuilder.methodOn(DbInfoController.class)
                                                        .instancesPdf(null))
                                        .toUri();
                } catch (IOException e) {
                        log.error("Error parsing instance pdf url", e);
                        return ChatopsRespBase.builder().statusCode(500)
                                        .msg("Error parsing instance pdf url" + e.getMessage())
                                        .build();
                }

                return listDisplayMenu(hostWildCast, hostButtons, uri.toString());
        }

        protected ChatopsRespBase instanceDisplayMenu(String instanceWildCast) {
                List<Database> databases = (this.databaseRepository
                                .findByTypeAndIdInstanceLikeIgnoreCase(
                                                getType(),
                                                instanceWildCast));

                List<IButtonView> databaseButtons = databases.stream().map(element -> (IButtonView) element)
                                .collect(Collectors.toList());

                URI uri;
                try {
                        uri = WebMvcLinkBuilder.linkTo(
                                        WebMvcLinkBuilder.methodOn(DbInfoController.class)
                                                        .instancesPdf(null))
                                        .toUri();
                } catch (IOException e) {
                        log.error("Error parsing instance pdf url", e);
                        return ChatopsRespBase.builder().statusCode(500)
                                        .msg("Error parsing instance pdf url" + e.getMessage())
                                        .build();
                }

                return listDisplayMenu(instanceWildCast, databaseButtons, uri.toString());
        }

        private ChatopsRespBase listDisplayMenu(String wildCast, List<IButtonView> list, String pdfUrl) {
                int listSize = list.size();
                log.debug(String.format("%-10s: %s", "Wildcast input", wildCast));
                log.debug("List size: {}", listSize);

                if (listSize == 0) {
                        return noInstanceFound;
                }

                if (listSize > 30) {
                        HealthCheck sybaseInstanceList = HealthCheck.builder()
                                        .keyword(wildCast + "|" + getType().name())
                                        .msg(String.format(
                                                        "List size=%s. PDF of list will be genearted.",
                                                        listSize))
                                        .url(pdfUrl)
                                        .statusCode(200)
                                        .token(secretConfig.getToken())
                                        .build();
                        return sybaseInstanceList;
                }

                String[] instanceOptions = list.stream().map(item -> item.getButtonView()).toArray(String[]::new);

                return SimpleMenu.builder().msg("Please select below").options(instanceOptions)
                                .statusCode(200).build();
        }

        protected SimpleMenu getCommandMenu(List<CmdGrpTypeEnum> cmdTypes) {
                List<CommandGroup> commandGroups = commandGroupRepository
                                .findByDbTypeAndCmdTypeInAndEnabledTrue(getType(), cmdTypes);

                /* use stream to get commands' id to array */
                SimpleMenu cmdMenu = SimpleMenu.builder().msg("Please select a command below")
                                .options(commandGroups.stream().map(CommandGroup::getId).toArray(String[]::new))
                                .statusCode(200)
                                .build();
                return cmdMenu;
        }

        protected String createHealthCheckKeyword(String commandGroupId, HashMap<String, String> storeVariablesMap)
                        throws JsonProcessingException {
                HealthCheckKeywordItem item = HealthCheckKeywordItem.builder()
                                .commandGroupId(commandGroupId)
                                .storeVariablesMap(storeVariablesMap).build();

                HealthCheckKeyword healthCheckKeyword = HealthCheckKeyword.builder()
                                .items(Collections.singletonList(item)).build();
                String healthCheckKeywordStr;

                ObjectMapper objectMapper = new ObjectMapper();
                healthCheckKeywordStr = objectMapper.writeValueAsString(healthCheckKeyword);

                return healthCheckKeywordStr;
        }
}