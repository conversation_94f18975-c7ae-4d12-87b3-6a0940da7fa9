import sys
import json

arg1 = sys.argv[1]
arg2 = sys.argv[2]
arg3 = sys.argv[3]
arg4 = sys.argv[4]
master_directory = sys.argv[5]
file_path = f"{master_directory}all_Target.json"

with open(file_path, "r") as file:
    json_data = json.load(file)

def loop_json(data, key_to_extract, name_field, name_value):
    result = []
    for key, value in data.items():
        if key == key_to_extract and data.get(arg3) == name_value:
            result.append(value)
        if isinstance(value, dict):
            result.extend(loop_json(value, key_to_extract, name_field, name_value))
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, dict):
                    result.extend(loop_json(item, key_to_extract, name_field, name_value))
    return result

ids = loop_json(json_data, arg2, arg3, arg4)
length = len(ids)
if ids is None or len(ids) == 0:
    ids_return = -1
else:
    ids_return = ids[0]
    
print(ids)

#print("arg1: "+arg1)
#print(arg1+arg2+arg3+arg4)
