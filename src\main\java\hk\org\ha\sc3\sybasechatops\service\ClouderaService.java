package hk.org.ha.sc3.sybasechatops.service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import hk.org.ha.sc3.sybasechatops.constant.ClouderaRoleEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.ClouderaCluster;
import hk.org.ha.sc3.sybasechatops.model.db.ClouderaComponent;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.repository.ClouderaClusterRepository;
import hk.org.ha.sc3.sybasechatops.repository.ClouderaComponentRepository;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

@Slf4j
@Service
public class ClouderaService implements ICommand {

        private HttpClient sslHttpClient;
        private ClouderaComponentRepository clouderaComponentRepository;
        private ClouderaClusterRepository clouderaClusterRepository;

        public ClouderaService(HttpClient sslHttpClient,
                        ClouderaComponentRepository clouderaComponentRepository,
                        ClouderaClusterRepository clouderaClusterRepository) {
                this.sslHttpClient = sslHttpClient;
                this.clouderaComponentRepository = clouderaComponentRepository;
                this.clouderaClusterRepository = clouderaClusterRepository;
        }

        private Mono<String> callRoleCommand(WebClient webClient, String clusterName, String serviceName,
                        String command, String roleId) {
                String path = String.format("/clusters/%s/services/%s/roleCommands/%s", clusterName, serviceName,
                                command);

                String requestBody = String.format("{\"items\":[\"%s\"]}", roleId);

                return webClient.post()
                                .uri(path)
                                .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                                .bodyValue(requestBody)
                                .retrieve()
                                .bodyToMono(String.class);
        }

        private Mono<String> chkRoleCommand(WebClient webClient, String clusterName, String serviceName) {
                String path = String.format("/clusters/%s/services/%s/roles", clusterName, serviceName);

                return webClient.get()
                                .uri(path)
                                .retrieve()
                                .bodyToMono(String.class);
        }

        public String[] getDistinctHost(ClouderaRoleEnum role, String clusterName) {
                List<ClouderaComponent> components = this.clouderaComponentRepository
                                .findDistinctByRoleAndClusterClusterName(role, clusterName);
                return components.stream()
                                .map(component -> component.getId().getHost()) // Extracting id.host
                                .toArray(String[]::new); // Collecting to String[]
        }

        public String[] getDistinctClusterName() {
                return this.clouderaClusterRepository.findAll().stream().map(c -> c.getClusterName())
                                .toArray(String[]::new);
        }

        public String[] getDistinctRole() {
                return this.clouderaComponentRepository.findDistinctRole().stream().toArray(String[]::new);
        }

        public ClouderaComponent getClouderaComponent(String host, ClouderaRoleEnum role) {
                ClouderaComponent component = clouderaComponentRepository.findByIdHostAndRole(host, role)
                                .orElseThrow(() -> new RuntimeException("Cloudrea component not found"));
                return component;
        }

        @Override
        public CommandResult execByCommand(Command command) throws Exception {

                String apiResult = "";
                WebClient webClient;

                switch (command.getId()) {
                        case "CLOUDERA_API_CHK_ROLE":
                                HashMap<String, String> storeVariable = command.getStoreVariables();
                                String service = storeVariable.get("service");
                                String clusterName = storeVariable.get("clusterName");
                                ClouderaCluster cluster = clouderaClusterRepository
                                                .findByClusterName(clusterName).orElseThrow(() -> new RuntimeException(
                                                                "Cloudera cluster name not found"));

                                webClient = this.getWebClient(cluster);
                                apiResult = this.chkRoleCommand(webClient, clusterName,
                                                service.toLowerCase()).block();
                                apiResult = Arrays.stream(apiResult.split("\n"))
                                                .filter(line -> line.contains("entityStatus") || line
                                                                .contains("hostname") || line.contains("roleUrl"))
                                                .collect(Collectors.joining("\n"));
                                break;
                        case "CLOUDERA_API_RESTART":
                        case "CLOUDERA_API_START":
                        case "CLOUDERA_API_STOP":
                                ClouderaComponent component = clouderaComponentRepository
                                                .findByIdHostAndIdInstance(command.getHost(), command.getInstance())
                                                .orElseThrow(() -> new RuntimeException(
                                                                "Cloudera component not found"));
                                webClient = this.getWebClient(component.getCluster());

                                apiResult = this.callRoleCommand(webClient, component.getCluster().getClusterName(),
                                                component.getRole().toString().toLowerCase(),
                                                command.getCommand().toLowerCase(),
                                                component.getId().getInstance()).block();
                                break;
                        default:
                                apiResult = "";
                }
                log.info("Cloudera API command executed: {}", command.getCommand());
                return CommandResult.builder().stdout(apiResult).stderr("").rc(0).build();
        }

        private WebClient getWebClient(ClouderaCluster cluster) {
                String apiBaseUrl = String.format("https://%s:7183/api/v%s", cluster.getApiHost(),
                                cluster.getApiVersion());
                return WebClient.builder()
                                .baseUrl(apiBaseUrl)
                                .defaultHeaders(headers -> headers.setBasicAuth(
                                                cluster.getUser(),
                                                cluster.getPassword()))
                                .clientConnector(new ReactorClientHttpConnector(this.sslHttpClient))
                                .build();
        }
}