package hk.org.ha.sc3.sybasechatops.model.db;

import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.Type;

import hk.org.ha.sc3.sybasechatops.constant.CmdReturnEnum;
import hk.org.ha.sc3.sybasechatops.constant.CmdTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "chatops_command", schema = "health_check", catalog = "health_check")
@Getter
@Setter
public class Command {
    @Id
    private String id;
    private String user;
    private String command;
    private Integer timeout;

    @Enumerated(EnumType.STRING)
    private CmdTypeEnum cmdType;

    @Enumerated(EnumType.STRING)
    private CmdReturnEnum returnType;

    @Type(type = "org.hibernate.type.NumericBooleanType")
    private boolean enabled;

    @OneToMany(mappedBy = "command")
    private List<CommandGroupMapping> commandGroups;

    @Transient
    private String instance;

    @Transient
    private String host;

    @Transient
    private HashMap<String, String> storeVariables;

    public String getCommand() {
        String cmdReplaced = this.command.replace("%instance%", this.instance).replace("%host%", this.host);
        for (Entry<String, String> entry : this.storeVariables.entrySet()) {
            cmdReplaced = cmdReplaced.replace("%" + entry.getKey() + "%", entry.getValue());
        }
        return cmdReplaced;
    }

}