package hk.org.ha.sc3.sybasechatops.model.webhook;

import hk.org.ha.sc3.sybasechatops.constant.webhook.WebhookTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class WebhookPdfReq extends WebhookReqBase {
    private String file_output_name;
    private String msg;
    private String pdfResult;

    @Builder
    public WebhookPdfReq(int roomId, String file_output_name, String msg, String pdfResult) {
        super(WebhookTypeEnum.multimedia, roomId);
        this.file_output_name = file_output_name;
        this.msg=msg;
        this.pdfResult=pdfResult;
    }
}
