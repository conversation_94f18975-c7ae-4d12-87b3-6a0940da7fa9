package hk.org.ha.sc3.sybasechatops.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.transaction.Transactional;

import org.springframework.stereotype.Service;

import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandGroup;
import hk.org.ha.sc3.sybasechatops.model.db.CommandGroupMapping;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import hk.org.ha.sc3.sybasechatops.model.db.StoreVariable;
import hk.org.ha.sc3.sybasechatops.repository.CommandGroupRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CommandGroupService {

    private CommandGroupRepository commandGroupRepository;
    private CommandContext cmdContext;
    private SshService sshService;
    private GrafanaService grafanaService;
    private ClouderaService clouderaService;
    private AnsibleService ansibleService;

    /* Constructor */
    public CommandGroupService(
            CommandGroupRepository commandGroupRepository,
            SshService sshService,
            GrafanaService grafanaService,
            ClouderaService clouderaService,
            AnsibleService ansibleService) {
        this.commandGroupRepository = commandGroupRepository;
        this.cmdContext = new CommandContext();

        this.sshService = sshService;
        this.grafanaService = grafanaService;
        this.clouderaService = clouderaService;
        this.ansibleService = ansibleService;
    }

    @Transactional
    public List<CommandResult> execByCommandGroupId(String commandGroupId, String hostName, String instanceName,
            HashMap<String, String> storeVariable)
            throws Exception {
        CommandGroup cmdGrp = this.commandGroupRepository.findById(commandGroupId)
                .orElseThrow(() -> new RuntimeException("Command group not found"));
        cmdGrp.setHost(hostName);
        cmdGrp.setInstance(instanceName);

        return execGroup(cmdGrp, storeVariable);
    }

    public List<CommandResult> execGroup(CommandGroup commandGroup, HashMap<String, String> storeVariablesMap)
            throws Exception {
        List<CommandGroupMapping> mappings = commandGroup.getCmdGrpMappings();
        List<CommandResult> cmdResults = new ArrayList<>();

        for (CommandGroupMapping mapping : mappings) {
            Command cmd = mapping.getCommand();
            if (!cmd.isEnabled()) {
                break;
            }
            cmd.setHost(commandGroup.getHost());
            cmd.setInstance(commandGroup.getInstance());
            cmd.setStoreVariables(storeVariablesMap);

            switch (cmd.getCmdType()) {
                case SSH:
                    cmdContext.setCommandExecutor(this.sshService);
                    break;
                case GRAFANA:
                    cmdContext.setCommandExecutor(this.grafanaService);
                    break;
                case CLOUDERA_API:
                    cmdContext.setCommandExecutor(this.clouderaService);
                    break;
                case ANSIBLE:
                    cmdContext.setCommandExecutor(this.ansibleService);
                    break;
            }

            // set icommand return
            CommandResult cmdResult = this.cmdContext.executeCommand(cmd);
            cmdResult.setPdfTitle(mapping.getPdfTitle());

            for (StoreVariable storeVariable : mapping.getStoreVariables()) {
                Optional<String> storeVariableValue = Optional.empty();
                Pattern pattern = Pattern.compile(storeVariable.getRegexpStr());

                Matcher matcher = pattern.matcher(cmdResult.getStdout());
                if (matcher.find()) {
                    storeVariableValue = Optional.of(matcher.group(1));
                }

                if (storeVariable.isMandatory()) {
                    storeVariablesMap.put(storeVariable.getName(),
                            storeVariableValue.orElseThrow(() -> new RuntimeException(
                                    "Cannot find the target store variable of pattern "
                                            + storeVariable.getRegexpStr())));
                }
            }

            cmdResults.add(cmdResult);
        }
        return cmdResults;
    }

}
