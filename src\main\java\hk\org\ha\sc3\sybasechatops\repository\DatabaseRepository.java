package hk.org.ha.sc3.sybasechatops.repository;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.model.db.Database;
import hk.org.ha.sc3.sybasechatops.model.db.id.DatabaseId;

public interface DatabaseRepository extends CrudRepository<Database, DatabaseId> {
    /* Find all database entities */
    List<Database> findAll();

    /* Find by property type */
    List<Database> findByType(DatabaseTypeEnum type);

    /* Find by property type and instance */
    List<Database> findByTypeAndIdInstance(DatabaseTypeEnum type, String instance);

    /* Find by property type and like search instance */
    List<Database> findByTypeAndIdInstanceLike(DatabaseTypeEnum type, String instance);

    /* Find by property type and like search upper cast instance */
    List<Database> findByTypeAndIdInstanceLikeIgnoreCase(DatabaseTypeEnum type, String instance);

    /* Find distinct host by type and like search the host string */
    @Query("SELECT DISTINCT id.host FROM Database WHERE type = ?1 AND id.host LIKE ?2 order by id.host")
    List<String> findDistinctHostByTypeAndIdHostLike(DatabaseTypeEnum type, String host);

    /* Find by database type and host name */
    List<Database> findByTypeAndIdHost(DatabaseTypeEnum type, String host);

    /* Find distinct project code */
    @Query("SELECT DISTINCT f.id.function.abbr FROM Database d inner join d.dbFunctions f WHERE d.type = ?1 AND f.id.function.abbr LIKE ?2 order by f.id.function.abbr")
    List<String> findDistinctProjectByTypeAndProjLike(DatabaseTypeEnum type, String proj);

    /* Find distinct host by proj */
    @Query("SELECT distinct d.id.host FROM DatabaseFunction f inner join f.id.database d WHERE d.type = ?1 AND f.id.function.abbr = ?2 order by d.id.host")
    List<String> findHostsByTypeAndProj(DatabaseTypeEnum type, String proj);

    @Query("SELECT d FROM DatabaseFunction f inner join f.id.database d WHERE d.type = ?1 AND f.id.function.abbr = ?2")
    List<Database> findByTypeAndProj(DatabaseTypeEnum type, String proj);
}
