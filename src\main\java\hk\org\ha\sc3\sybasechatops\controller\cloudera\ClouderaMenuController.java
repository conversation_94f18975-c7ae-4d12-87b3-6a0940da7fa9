package hk.org.ha.sc3.sybasechatops.controller.cloudera;

import java.util.Collections;
import java.util.HashMap;
import java.util.regex.Pattern;
import com.fasterxml.jackson.core.JsonProcessingException;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import hk.org.ha.sc3.sybasechatops.config.NotificationConfig;
import hk.org.ha.sc3.sybasechatops.config.SecretConfig;
import hk.org.ha.sc3.sybasechatops.constant.ClouderaRoleEnum;
import hk.org.ha.sc3.sybasechatops.constant.CmdGrpTypeEnum;
import hk.org.ha.sc3.sybasechatops.constant.DatabaseTypeEnum;
import hk.org.ha.sc3.sybasechatops.controller.BaseMenuController;
import hk.org.ha.sc3.sybasechatops.model.Approval;
import hk.org.ha.sc3.sybasechatops.model.ChatopsReq;
import hk.org.ha.sc3.sybasechatops.model.ChatopsRespBase;
import hk.org.ha.sc3.sybasechatops.model.Reply;
import hk.org.ha.sc3.sybasechatops.model.SimpleMenu;
import hk.org.ha.sc3.sybasechatops.model.db.ClouderaComponent;
import hk.org.ha.sc3.sybasechatops.repository.CommandGroupRepository;
import hk.org.ha.sc3.sybasechatops.repository.DatabaseRepository;
import hk.org.ha.sc3.sybasechatops.service.ClouderaService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("cloudera")
public class ClouderaMenuController extends BaseMenuController {

    private ClouderaService clouderaService;

    public ClouderaMenuController(DatabaseRepository databaseRepository,
            CommandGroupRepository commandGroupRepository, ClouderaService clouderaService,
            NotificationConfig notificationConfig, SecretConfig secretConfig) {
        super(databaseRepository, commandGroupRepository, ClouderaHealthCheckController.class, notificationConfig,
                secretConfig);
        this.clouderaService = clouderaService;
    }

    @PostMapping
    public ChatopsRespBase baseMenu(@RequestBody ChatopsReq requestBody) {
        log.debug("Menu entered");
        log.debug(String.format("%-10s: %s", "Env", requestBody.getChatopsEnv()));
        log.debug(String.format("%-10s: %s", "CorpID", requestBody.getCorpID()));
        log.debug(String.format("%-10s: %s", "MsgChain", requestBody.getMsgChain()));
        log.debug(String.format("%-10s: %s", "Keyword", requestBody.getKeyword()));

        return getBaseResponse(requestBody.getMsgChain());
    }

    public ChatopsRespBase getBaseResponse(String msgChain) {

        /*
         * msg chain sample
         * 1. @itbot|Cloudera|sc3_sand01|Kafka|CLOUDERA_API_STOP|bdvmc2a|Approve|Process
         * 2. @itbot|Cloudera|sc3_sand01|Kafka|CLOUDERA_API_STOP|all|Approve|Process (To
         * Do)
         * 3. @itbot|Cloudera|sc3_sand01|HIVE_ON_TEZ|CLOUDERA_API_CHK_ROLE
         * 
         * msgChainArr:
         * 2. cluster name
         * 3. service
         * 4. button name (commandGroupId)
         * 5. host(s)
         */

        String[] msgChainArr = msgChain.split("\\|");

        if (msgChain.equals("@itbot|Cloudera")) {
            return SimpleMenu.builder().msg("Please select cluster name below")
                    .options(this.clouderaService.getDistinctClusterName()).statusCode(200)
                    .build();
        }

        if (Pattern.matches("@itbot\\|Cloudera\\|[^|]+", msgChain)) {
            final String[] serviceOptions = this.clouderaService.getDistinctRole();

            SimpleMenu clouderaBaseMenu = SimpleMenu.builder().msg("Please select service below")
                    .options(serviceOptions).statusCode(200)
                    .build();
            return clouderaBaseMenu;
        }

        if (Pattern.matches("@itbot\\|Cloudera\\|[^|]+\\|[^|]+", msgChain)) {
            return this.getCommandMenu(Collections.singletonList(CmdGrpTypeEnum.INSTANCE));
        }

        if (Pattern.matches("@itbot\\|Cloudera\\|[^|]+\\|[^|]+\\|[^|]+", msgChain)) {
            if (msgChainArr[4].equals("CLOUDERA_API_CHK_ROLE")) {
                /*
                 * {cluster_name}:{service}|{command}
                 */
                HashMap<String, String> clusterServiceMap = new HashMap<String, String>();
                clusterServiceMap.put("clusterName", msgChainArr[2]);
                clusterServiceMap.put("service", msgChainArr[3]);
                String healthCheckKeywordStr;
                try {
                    healthCheckKeywordStr = this.createHealthCheckKeyword(msgChainArr[4], clusterServiceMap);
                } catch (JsonProcessingException e) {
                    log.error("Error converting map to JSON", e);
                    return Reply.builder().msg("Error processing json").statusCode(500).build();
                }
                return this.postHealthCheckAction(healthCheckKeywordStr);
            } else {
                String[] hosts = this.clouderaService.getDistinctHost(ClouderaRoleEnum.valueOf(msgChainArr[3]),
                        msgChainArr[2]);
                return SimpleMenu.builder().msg("Please select hosts below").options(hosts).statusCode(200).build();
            }
        }

        if (Pattern.matches("@itbot\\|Cloudera\\|[^|]+\\|[^|]+\\|[^|]+\\|[^|]+", msgChain)) {
            return Approval.builder().keyword("approval").msg(
                    "Hi, APPROVERS. Please approve the request by REQUESTER.")
                    .build();
        }

        if (Pattern.matches("@itbot\\|Cloudera\\|[^|]+\\|[^|]+\\|[^|]+\\|[^|]+\\|Approve\\|Process", msgChain)) {
            // "bdvmc2c:hive_on_tez-HIVESERVER2-32b7cc0f686999f6e9aee7e86d47eaf4|Stop"
            ClouderaComponent component = this.clouderaService.getClouderaComponent(msgChainArr[5],
                    ClouderaRoleEnum.valueOf(msgChainArr[3]));
            String host = component.getId().getHost();
            String instance = component.getId().getInstance();
            String button = msgChainArr[4];
            String healthCheckKeyword = String.format("%s:%s|%s", host, instance, button);
            return this.postApprovedHealthCheckAction(healthCheckKeyword, msgChainArr[4]);
        }

        return Reply.builder().msg("Unknown option").statusCode(200).build();

    }

    @Override
    public DatabaseTypeEnum getType() {
        return DatabaseTypeEnum.CLOUDERA;
    }

}
