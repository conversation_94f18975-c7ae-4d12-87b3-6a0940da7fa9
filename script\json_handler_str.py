import sys
import json

arg1 = sys.argv[1]
master_directory = sys.argv[2]

file_path = f"{master_directory}Target.json"

with open(file_path, "r") as file:
    json_data = json.load(file)

#status = [result["status"] for result in json_data[]]
status_value = json_data.get("status")

#print(status_value)
if status_value is None or len(json_data) == 0:
    print(f"Host not found")
else:
    print(f"The agent status is: {status_value}")

#print(f"The agent status is: {status_value}")