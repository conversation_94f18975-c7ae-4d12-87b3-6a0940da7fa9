package hk.org.ha.sc3.sybasechatops.service;

import java.time.Duration;

import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Wait;
import org.springframework.stereotype.Service;

import hk.org.ha.sc3.sybasechatops.config.GrafanaConfig;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class GrafanaService implements ICommand {

    private static final int WINDOW_WIDTH = 1920;
    private static final int WINDOW_HEIGHT_PADDING = 200;

    private WebDriverService webDriverService;
    private GrafanaConfig grafanaConfig;

    public GrafanaService(WebDriverService webDriverService, GrafanaConfig grafanaConfig) {
        this.webDriverService = webDriverService;
        this.grafanaConfig = grafanaConfig;
    }

    private WebElement waitByClass(String className, FirefoxDriver driver) {
        Wait<WebDriver> wait = new FluentWait<WebDriver>(driver)
                .withTimeout(Duration.ofSeconds(grafanaConfig.getWaitTimeoutSecond()))
                .pollingEvery(Duration.ofSeconds(grafanaConfig.getWaitTimeoutSecond()))
                .ignoring(NoSuchElementException.class);

        return wait.until(d -> d.findElement(By.className(className)));
    }

    private void login(FirefoxDriver driver) {
        WebElement userBox = driver.findElement(By.name("user"));
        WebElement passwordBox = driver.findElement(By.id("current-password"));
        WebElement loginButton = driver.findElement(By.className("css-14g7ilz-button"));

        userBox.sendKeys(grafanaConfig.getUsername());
        passwordBox.sendKeys(grafanaConfig.getPassword());
        loginButton.click();
    }

    private int getDashboardHeight(FirefoxDriver driver) {
        WebElement scroll = this.waitByClass("react-grid-layout", driver);
        JavascriptExecutor js = (JavascriptExecutor) driver;

        return (int) (long) js.executeScript("return arguments[0].clientHeight;", scroll);
    }

    private String navigateDashboard(FirefoxDriver driver, String dashBoardUrl, String... queryParameters) {
        String url = String.format("%s%s?orgId=1&forceLogin=true", grafanaConfig.getBaseUrl(), dashBoardUrl);
        for (String parameter : queryParameters) {
            url += "&" + parameter;
        }
        driver.navigate().to(url);
        return url;
    }

    private void setWindowToDashboardHeight(FirefoxDriver driver) {
        driver.manage().window()
                .setSize(new Dimension(WINDOW_WIDTH, this.getDashboardHeight(driver) + WINDOW_HEIGHT_PADDING));
    }

    private CommandResult executeGrafanaCommand(String dashboardUrl, String... queryParameters) throws Exception {
        FirefoxDriver driver = null;
        long hcStartTime = System.currentTimeMillis();
        long hcEndTime;
        String file;
        String currentUrl;
        double elapsedTime;
        try {
            driver = this.webDriverService.firefoxDriver();
            currentUrl = this.navigateDashboard(driver, dashboardUrl, queryParameters);
            this.login(driver);
            this.setWindowToDashboardHeight(driver);
            Thread.sleep(grafanaConfig.getScreenshotDelayMs());
            file = webDriverService.takeSnapShot(driver);
            log.info("Grafana command executed successfully. URL: {}", currentUrl);
            hcEndTime = System.currentTimeMillis();
            elapsedTime = (double) ((hcEndTime - hcStartTime)) / 1000;

        } finally {
            if (driver != null) {
                driver.quit();
            }
        }
        return CommandResult.builder()
                .file(file)
                .command(currentUrl)
                .elapsedTime(elapsedTime)
                .build();
    }

    public CommandResult getDbInstanceStatusByProject() throws Exception {
        log.info("getDbInstanceStatusByProject started");
        return executeGrafanaCommand("i_S-7EYIz/sc3-db-instance-status-by-project");
    }

    public CommandResult getDbInstanceStatusOverview() throws Exception {
        log.info("getDbInstanceStatusOverview started");
        return executeGrafanaCommand("GFZzZrLIz/sc3-db-instance-status-overview");
    }

    public CommandResult getOracleHostMon(String host) throws Exception {
        log.info("getOracleHostMon started");
        String oemTargetName = String.format("var-oem_gf_target_name=%s.%s", host, grafanaConfig.getServerDomain());
        return executeGrafanaCommand("0B3NDbAIk/sc3-oracle-db-host-level-monitoring", oemTargetName);
    }

    public CommandResult getOracleInstanceMon(String host, String sid) throws Exception {
        log.info("getOracleInstanceMon started");
        String oemTargetName = String.format("var-oem_gf_target_name=%s_%s", sid, host);
        return executeGrafanaCommand("SlftC-ASz/sc3-oracle-db-instance-level-monitoring", oemTargetName);
    }

    @Override
    public CommandResult execByCommand(Command command) throws Exception {
        switch (command.getCommand()) {
            case "getDbInstanceStatusByProject":
                return this.getDbInstanceStatusByProject();
            case "getDbInstanceStatusOverview":
                return this.getDbInstanceStatusOverview();
            case "getOracleHostMon":
                return this.getOracleHostMon(command.getHost());
            case "getOracleInstanceMon":
                return this.getOracleInstanceMon(command.getHost(), command.getInstance());
        }
        log.info("Grafana command executed: {}", command.getCommand());
        return CommandResult.builder().rc(-1).stderr("Unknown command: " + command.getCommand()).build();
    }

}