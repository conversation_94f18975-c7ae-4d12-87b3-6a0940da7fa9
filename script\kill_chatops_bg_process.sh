# Check the number of arguments
if [ $# -ne 1 ]; then
    echo "Usage: $0 {dev|sit|prd}"
    echo "** fg means foreground process. bg means background process."
    exit 1
fi

declare env=$1

# Validate the argument value
if [ "${env}" != "dev" ] && [ "${env}" != "sit" ] && [ "${env}" != "prd" ]; then
    echo "Invalid environment. Allowed values: dev, sit, prd"
    exit 1
fi

declare script_path=$(dirname "$(readlink -f "$0")")
declare process=$(ps -ef | grep ${script_path}/${env}/chatops-spring.*.jar | grep -v grep)
echo "Process:"
echo "${process}"
kill -9 $(echo "${process}" | awk '{print $2}')

declare kill_rc=$?
if [ ${kill_rc} == 0 ];then
    echo
    echo "${env} process killed"
fi
