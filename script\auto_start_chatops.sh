#!/bin/bash

log() {
    local sentence="$1"
    local datetime=$(date +"%y%m%dT%H%M%S")
    echo "$datetime - $sentence" | tee -a "${log_path}"
}

if [ $# -ne 2 ]; then
    echo "Usage: $0 {dev|sit|prd} {java_path}"
    exit 1
fi

declare env=$1
declare java_path=$2
declare script_path=$(dirname "$(readlink -f "$0")")
declare process=$(ps -ef | grep ${script_path}/${env}/chatops-spring.*.jar | grep -v grep)
declare retry_count_file="${script_path}/auto_restart_chatops_count_${env}.txt"
declare log_path="${script_path}/auto_retry_${env}.log"

if [[ -z "$process" ]]; then

    # Check if the file exists
    if [ -f "$retry_count_file" ]; then

        # Check if the file contains a number only
        if grep -q '^[0-9]\+$' "$retry_count_file"; then

            declare -i retry_count=$(cat "$retry_count_file")

            # Check if the number is greater than 3
            if [ "$retry_count" -gt 3 ]; then
                log "The retry count is greater than 3."
                log "Auto restart will not be executed."
                exit 2
            else
                log "The retry count is ${retry_count}. Auto restarting..."
                retry_count+=1
                echo "$retry_count" > "${retry_count_file}"
                (
                echo "MIME-Version: 1.0"
                echo "Subject: [$env] Chatops auto restart triggered"
                echo "To: <EMAIL>"
                echo "Retry count: $retry_count" ) | /usr/sbin/sendmail -t
                ${script_path}/start_chatops.sh ${env} bg ${java_path}

            fi
        else
            log "Retry count file does not contain a number only. Reset retry file..."
            echo "0" > "${retry_count_file}"
            exit 3
        fi
    else
        log "Restart count file does not exist. Reset retry file..."
        echo "0" > "${retry_count_file}"
        exit 1
    fi

else

    declare -i retry_count=$(cat "$retry_count_file")

    if [ "$retry_count" -ne 0 ]; then
        log "Status normal. Reset retry file..."
        echo "0" > "${retry_count_file}"
    fi

fi